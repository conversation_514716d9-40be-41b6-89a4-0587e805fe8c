// Core Types for Le Pizzeria App

export interface MenuItem {
  id: string;
  name: string;
  description: string;
  price: number;
  image: string;
  category: MenuCategory;
  rating: number;
  prepTime: string;
  ingredients: string[];
  allergens: string[];
  isVegetarian: boolean;
  isVegan: boolean;
  isGlutenFree: boolean;
  customizations?: Customization[];
  nutritionInfo?: NutritionInfo;
}

export interface Customization {
  id: string;
  name: string;
  type: 'size' | 'crust' | 'toppings' | 'sauce' | 'extras';
  options: CustomizationOption[];
  required: boolean;
  maxSelections?: number;
}

export interface CustomizationOption {
  id: string;
  name: string;
  price: number;
  isDefault?: boolean;
}

export interface CartItem {
  id: string;
  menuItem: MenuItem;
  quantity: number;
  customizations: { [key: string]: string[] };
  totalPrice: number;
  specialInstructions?: string;
}

export interface Order {
  id: string;
  items: CartItem[];
  subtotal: number;
  deliveryFee: number;
  tax: number;
  total: number;
  status: OrderStatus;
  orderTime: Date;
  estimatedDelivery: Date;
  deliveryAddress: Address;
  paymentMethod: PaymentMethod;
  trackingInfo?: TrackingInfo;
}

export interface TrackingInfo {
  status: OrderStatus;
  estimatedTime: string;
  currentLocation?: {
    latitude: number;
    longitude: number;
  };
  driverInfo?: {
    name: string;
    phone: string;
    rating: number;
  };
  updates: TrackingUpdate[];
}

export interface TrackingUpdate {
  timestamp: Date;
  status: OrderStatus;
  message: string;
}

export interface Address {
  id: string;
  label: string;
  street: string;
  city: string;
  postalCode: string;
  country: string;
  coordinates?: {
    latitude: number;
    longitude: number;
  };
  isDefault: boolean;
}

export interface PaymentMethod {
  id: string;
  type: 'apple_pay' | 'google_pay' | 'credit_card' | 'debit_card';
  last4?: string;
  brand?: string;
  isDefault: boolean;
}

export interface User {
  id: string;
  name: string;
  email: string;
  phone: string;
  avatar?: string;
  addresses: Address[];
  paymentMethods: PaymentMethod[];
  orderHistory: Order[];
  favorites: string[];
  preferences: UserPreferences;
}

export interface UserPreferences {
  notifications: boolean;
  darkMode: boolean;
  language: string;
  dietaryRestrictions: string[];
}

export interface Event {
  id: string;
  title: string;
  description: string;
  date: Date;
  startTime: string;
  endTime: string;
  location: string;
  price: number;
  capacity: number;
  availableTickets: number;
  image: string;
  category: EventCategory;
  features: string[];
}

export interface EventTicket {
  id: string;
  eventId: string;
  quantity: number;
  totalPrice: number;
  purchaseDate: Date;
  qrCode: string;
  status: 'active' | 'used' | 'cancelled';
}

export interface NutritionInfo {
  calories: number;
  protein: number;
  carbs: number;
  fat: number;
  fiber: number;
  sodium: number;
}

export type MenuCategory = 'pizza' | 'pasta' | 'salads' | 'desserts' | 'drinks' | 'sides' | 'popular';
export type OrderStatus = 'pending' | 'confirmed' | 'preparing' | 'ready' | 'out_for_delivery' | 'delivered' | 'cancelled';
export type EventCategory = 'live_music' | 'wine_tasting' | 'cooking_class' | 'private_dining' | 'special_event';

export interface NavigationProps {
  navigation: any;
  route: any;
}
