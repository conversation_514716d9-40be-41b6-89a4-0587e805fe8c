{"expo": {"name": "Le Pizzeria", "slug": "lepizzeria-mobile-app", "version": "1.0.0", "orientation": "portrait", "icon": "./assets/icon.png", "userInterfaceStyle": "light", "splash": {"image": "./assets/splash.png", "resizeMode": "contain", "backgroundColor": "#dc2626"}, "assetBundlePatterns": ["**/*"], "ios": {"supportsTablet": true, "bundleIdentifier": "com.lepizzeria.mobileapp", "buildNumber": "1.0.0", "infoPlist": {"NSLocationWhenInUseUsageDescription": "This app uses location to provide delivery services and find nearby restaurants.", "NSCameraUsageDescription": "This app uses camera to scan QR codes for event tickets.", "NSMicrophoneUsageDescription": "This app uses microphone for voice orders (optional feature)."}}, "android": {"adaptiveIcon": {"foregroundImage": "./assets/adaptive-icon.png", "backgroundColor": "#dc2626"}, "package": "com.lepizzeria.mobileapp", "versionCode": 1, "permissions": ["ACCESS_FINE_LOCATION", "ACCESS_COARSE_LOCATION", "CAMERA", "RECORD_AUDIO"]}, "web": {"favicon": "./assets/favicon.png", "bundler": "metro"}, "plugins": ["expo-location", "expo-notifications", ["expo-apple-authentication", {"appleTeamId": "YOUR_APPLE_TEAM_ID"}]], "extra": {"eas": {"projectId": "your-project-id"}}}}