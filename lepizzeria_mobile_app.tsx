import React, { useState } from 'react';
import { ChevronR<PERSON>, MapPin, Clock, Star, Plus, Heart, Search } from 'lucide-react';

export default function LePizzeriaApp() {
  const [activeCategory, setActiveCategory] = useState('Popular');
  const [favorites, setFavorites] = useState(new Set());

  // Logo component matching the brand
  const LePizzeriaLogo = () => (
    <div className="flex items-center space-x-1">
      <div className="flex flex-col items-start">
        <div className="flex items-center space-x-2">
          <div className="w-8 h-0.5 bg-green-500 rounded-full"></div>
          <span className="text-white font-black text-sm tracking-wider" style={{fontFamily: 'Lufga, system-ui, sans-serif'}}>LE</span>
        </div>
        <div className="flex items-center mt-1">
          <h1 className="text-white font-black text-3xl tracking-tight leading-none" style={{fontFamily: 'Lufga, system-ui, sans-serif'}}>
            PIZZERIA
          </h1>
          <span className="text-white font-bold text-xl ml-1 bg-black px-2 py-1 rounded" style={{fontFamily: 'Lufga, system-ui, sans-serif'}}>24</span>
        </div>
        <div className="w-8 h-0.5 bg-red-500 rounded-full mt-1"></div>
      </div>
    </div>
  );

  const toggleFavorite = (id) => {
    const newFavorites = new Set(favorites);
    if (newFavorites.has(id)) {
      newFavorites.delete(id);
    } else {
      newFavorites.add(id);
    }
    setFavorites(newFavorites);
  };

  const categories = ['Popular', 'Pizza', 'Pasta', 'Salads', 'Desserts', 'Drinks'];

  const featuredItems = [
    {
      id: 1,
      name: 'Margherita Classica',
      description: 'Fresh mozzarella, basil, tomato sauce',
      price: 'R89',
      image: '🍕',
      rating: 4.8,
      time: '15-20 min'
    },
    {
      id: 2,
      name: 'Quattro Stagioni',
      description: 'Mushrooms, ham, artichokes, olives',
      price: 'R125',
      image: '🍕',
      rating: 4.9,
      time: '18-25 min'
    },
    {
      id: 3,
      name: 'Pasta Carbonara',
      description: 'Creamy sauce, pancetta, parmesan',
      price: 'R95',
      image: '🍝',
      rating: 4.7,
      time: '12-18 min'
    }
  ];

  return (
    <div className="min-h-screen bg-gray-50 max-w-sm mx-auto relative overflow-hidden" style={{fontFamily: 'Lufga, system-ui, sans-serif'}}>
      {/* Import Lufga Font */}
      <link href="https://fonts.googleapis.com/css2?family=Lufga:wght@400;500;600;700;800;900&display=swap" rel="stylesheet" />
      {/* Status Bar */}
      <div className="bg-white px-4 py-2 flex justify-between items-center text-sm font-medium">
        <span style={{fontFamily: 'Lufga, system-ui, sans-serif'}}>9:41</span>
        <div className="flex space-x-1">
          <div className="w-4 h-2 bg-black rounded-sm"></div>
          <div className="w-6 h-2 bg-black rounded-sm"></div>
          <div className="w-6 h-2 bg-green-500 rounded-sm"></div>
        </div>
      </div>

      {/* Header */}
      <div className="bg-white px-6 py-4 shadow-sm">
        <div className="flex items-center justify-between mb-4">
          <div>
            <div className="flex items-center space-x-2 mb-1">
              <MapPin className="w-4 h-4 text-gray-600" />
              <span className="text-sm text-gray-600">Delivering to</span>
            </div>
            <span className="font-semibold text-gray-900">Observatory, Cape Town</span>
          </div>
          <div className="w-10 h-10 bg-gray-200 rounded-full flex items-center justify-center">
            <span className="text-lg">👤</span>
          </div>
        </div>

        {/* Search Bar */}
        <div className="relative">
          <Search className="absolute left-3 top-1/2 transform -y-1/2 w-5 h-5 text-gray-400" />
          <input 
            type="text" 
            placeholder="Search for pizza, pasta..." 
            className="w-full bg-gray-100 rounded-xl py-3 pl-10 pr-4 text-sm focus:outline-none focus:ring-2 focus:ring-red-500"
          />
        </div>
      </div>

      {/* Brand Section */}
      <div className="bg-gradient-to-br from-red-600 to-red-700 px-6 py-8 text-white relative overflow-hidden">
        <div className="absolute top-0 right-0 w-32 h-32 bg-white opacity-5 rounded-full transform translate-x-16 -translate-y-16"></div>
        <div className="absolute bottom-0 left-0 w-24 h-24 bg-white opacity-5 rounded-full transform -translate-x-8 translate-y-8"></div>
        
        <div className="relative z-10">
          {/* Le Pizzeria 24 Logo - Exact Recreation */}
          <div className="mb-6">
            <div className="flex items-end justify-between w-full max-w-xs">
              {/* LE with green underline */}
              <div className="flex flex-col items-start">
                <span className="text-white font-black text-lg" style={{fontFamily: 'Lufga, system-ui, sans-serif', letterSpacing: '0.05em'}}>LE</span>
                <div className="w-8 h-1 bg-green-400 rounded-sm mt-1"></div>
              </div>
              
              {/* Main PIZZERIA text */}
              <h1 className="text-white font-black text-3xl tracking-tighter leading-none mx-2" style={{fontFamily: 'Lufga, system-ui, sans-serif', letterSpacing: '-0.02em'}}>
                PIZZERIA
              </h1>
              
              {/* 24 with red underline */}
              <div className="flex flex-col items-end">
                <span className="text-white font-black text-lg" style={{fontFamily: 'Lufga, system-ui, sans-serif'}}>24</span>
                <div className="w-8 h-1 bg-red-400 rounded-sm mt-1"></div>
              </div>
            </div>
          </div>
          
          <p className="text-red-100 text-sm mb-4" style={{fontFamily: 'Lufga, system-ui, sans-serif'}}>Authentic Italian flavors, delivered fresh to your door</p>
          
          <div className="flex items-center space-x-4 text-sm" style={{fontFamily: 'Lufga, system-ui, sans-serif'}}>
            <div className="flex items-center space-x-1">
              <Clock className="w-4 h-4" />
              <span>25-35 min</span>
            </div>
            <div className="flex items-center space-x-1">
              <Star className="w-4 h-4 text-yellow-400 fill-current" />
              <span>4.8 (2.1k)</span>
            </div>
          </div>
        </div>
      </div>

      {/* Categories */}
      <div className="bg-white px-4 py-4">
        <div className="flex space-x-2 overflow-x-auto">
          {categories.map((category) => (
            <button
              key={category}
              onClick={() => setActiveCategory(category)}
              className={`px-4 py-2 rounded-full text-sm font-medium whitespace-nowrap transition-all ${
                activeCategory === category
                  ? 'bg-red-600 text-white shadow-md'
                  : 'bg-gray-100 text-gray-600 hover:bg-gray-200'
              }`}
            >
              {category}
            </button>
          ))}
        </div>
      </div>

      {/* Featured Items */}
      <div className="px-6 py-4">
        <div className="flex items-center justify-between mb-4">
          <h2 className="text-xl font-bold text-gray-900">Featured Today</h2>
          <button className="flex items-center space-x-1 text-red-600 text-sm font-medium">
            <span>See all</span>
            <ChevronRight className="w-4 h-4" />
          </button>
        </div>

        <div className="space-y-4">
          {featuredItems.map((item) => (
            <div key={item.id} className="bg-white rounded-2xl p-4 shadow-sm border border-gray-100">
              <div className="flex space-x-4">
                <div className="w-20 h-20 bg-gray-100 rounded-xl flex items-center justify-center text-3xl">
                  {item.image}
                </div>
                
                <div className="flex-1">
                  <div className="flex items-start justify-between mb-1">
                    <h3 className="font-semibold text-gray-900 text-base">{item.name}</h3>
                    <button 
                      onClick={() => toggleFavorite(item.id)}
                      className="p-1"
                    >
                      <Heart 
                        className={`w-5 h-5 ${
                          favorites.has(item.id) 
                            ? 'text-red-500 fill-current' 
                            : 'text-gray-400'
                        }`} 
                      />
                    </button>
                  </div>
                  
                  <p className="text-gray-600 text-sm mb-2 leading-relaxed">{item.description}</p>
                  
                  <div className="flex items-center justify-between">
                    <div className="flex items-center space-x-3">
                      <span className="font-bold text-lg text-gray-900">{item.price}</span>
                      <div className="flex items-center space-x-1">
                        <Star className="w-3 h-3 text-yellow-400 fill-current" />
                        <span className="text-xs text-gray-600">{item.rating}</span>
                      </div>
                      <span className="text-xs text-gray-500">• {item.time}</span>
                    </div>
                    
                    <button className="bg-red-600 hover:bg-red-700 text-white w-8 h-8 rounded-full flex items-center justify-center transition-colors">
                      <Plus className="w-4 h-4" />
                    </button>
                  </div>
                </div>
              </div>
            </div>
          ))}
        </div>
      </div>

      {/* Quick Actions */}
      <div className="px-6 py-4">
        <div className="grid grid-cols-2 gap-3">
          <button className="bg-green-50 border border-green-200 rounded-xl p-4 text-center hover:bg-green-100 transition-colors">
            <div className="text-2xl mb-2">🏃‍♂️</div>
            <span className="text-sm font-medium text-green-800">Quick Order</span>
            <p className="text-xs text-green-600 mt-1">Reorder favorites</p>
          </button>
          
          <button className="bg-blue-50 border border-blue-200 rounded-xl p-4 text-center hover:bg-blue-100 transition-colors">
            <div className="text-2xl mb-2">🎯</div>
            <span className="text-sm font-medium text-blue-800">Track Order</span>
            <p className="text-xs text-blue-600 mt-1">Live delivery status</p>
          </button>
        </div>
      </div>

      {/* Bottom Navigation */}
      <div className="fixed bottom-0 left-1/2 transform -translate-x-1/2 w-full max-w-sm bg-white border-t border-gray-200 px-6 py-4">
        <div className="grid grid-cols-4 gap-4">
          <button className="flex flex-col items-center space-y-1">
            <div className="w-6 h-6 bg-red-600 rounded"></div>
            <span className="text-xs font-medium text-red-600">Home</span>
          </button>
          <button className="flex flex-col items-center space-y-1">
            <div className="w-6 h-6 bg-gray-300 rounded"></div>
            <span className="text-xs text-gray-500">Menu</span>
          </button>
          <button className="flex flex-col items-center space-y-1">
            <div className="w-6 h-6 bg-gray-300 rounded"></div>
            <span className="text-xs text-gray-500">Orders</span>
          </button>
          <button className="flex flex-col items-center space-y-1">
            <div className="w-6 h-6 bg-gray-300 rounded"></div>
            <span className="text-xs text-gray-500">Profile</span>
          </button>
        </div>
      </div>

      {/* Safe Area Bottom Padding */}
      <div className="h-20"></div>
    </div>
  );
}