# Le Pizzeria Mobile App

A comprehensive React Native mobile application for Le Pizzeria restaurant, featuring food ordering, delivery tracking, and event ticketing.

## Features

### 🍕 Food Ordering & Delivery
- **Menu Browsing**: Browse pizzas, pastas, salads, desserts, and drinks with high-quality images
- **Customization**: Select toppings, crust types, and portion sizes
- **Order Scheduling**: Choose ASAP delivery or schedule for later
- **Real-time Tracking**: Live order status and delivery tracking
- **Payment Integration**: Apple Pay, Google Pay, and credit/debit cards
- **Order History**: View past orders and reorder quickly

### 🎫 Event Ticketing
- **Browse Events**: Wine tastings, cooking classes, live music, and private dining
- **Event Details**: Comprehensive information with features and pricing
- **Ticket Booking**: Seamless booking experience with Apple Events integration
- **Event Management**: View upcoming events and manage tickets

### 📱 iOS Design Excellence
- **Human Interface Guidelines**: Follows iOS design principles
- **Warm Branding**: Red and black color scheme with Lufga typography
- **Accessibility**: High contrast, scalable fonts, and screen reader support
- **Smooth Animations**: Micro-interactions for enhanced user experience

## Tech Stack

- **Frontend**: React Native with Expo
- **Navigation**: React Navigation 6
- **State Management**: React Hooks and Context API
- **UI Components**: Custom components following iOS design system
- **Maps**: React Native Maps for delivery tracking
- **Notifications**: Expo Notifications
- **Authentication**: Apple Authentication & Google Auth
- **Payments**: Apple Pay and Google Pay integration

## Getting Started

### Prerequisites

- Node.js (v16 or higher)
- npm or yarn
- Expo CLI
- iOS Simulator (for iOS development)
- Android Studio (for Android development)

### Installation

1. **Clone the repository**
   ```bash
   git clone <repository-url>
   cd lepizzeria-mobile-app
   ```

2. **Install dependencies**
   ```bash
   npm install
   # or
   yarn install
   ```

3. **Start the development server**
   ```bash
   npm start
   # or
   yarn start
   ```

4. **Run on iOS**
   ```bash
   npm run ios
   # or
   yarn ios
   ```

5. **Run on Android**
   ```bash
   npm run android
   # or
   yarn android
   ```

### Development Setup

1. **Install Expo CLI globally**
   ```bash
   npm install -g @expo/cli
   ```

2. **Install iOS Simulator** (macOS only)
   - Download Xcode from the App Store
   - Install iOS Simulator through Xcode

3. **Install Android Studio**
   - Download from [developer.android.com](https://developer.android.com/studio)
   - Set up Android Virtual Device (AVD)

## Project Structure

```
lepizzeria-mobile-app/
├── App.tsx                 # Main app component with navigation
├── app.json               # Expo configuration
├── package.json           # Dependencies and scripts
├── components/
│   └── screens/           # Screen components
│       ├── HomeScreen.tsx
│       ├── MenuScreen.tsx
│       ├── ItemDetailScreen.tsx
│       ├── CartScreen.tsx
│       ├── OrderTrackingScreen.tsx
│       ├── EventsScreen.tsx
│       └── ProfileScreen.tsx
├── types/
│   └── index.ts           # TypeScript type definitions
├── utils/
│   └── mockData.ts        # Mock data for development
└── delivery-loading-screen.tsx  # Loading screen component
```

## Key Components

### HomeScreen
- Restaurant branding and hero section
- Featured menu items
- Quick actions (reorder, track order)
- Category navigation

### MenuScreen
- Full menu browsing with categories
- Search functionality
- Item filtering and sorting
- Add to cart functionality

### ItemDetailScreen
- Detailed item information
- Customization options (size, toppings, etc.)
- Nutritional information
- Add to cart with quantity selection

### CartScreen
- Shopping cart management
- Order summary with pricing
- Promo code application
- Delivery information

### OrderTrackingScreen
- Real-time order status
- Delivery tracking map
- Driver information
- Order details and timeline

### EventsScreen
- Event browsing and filtering
- Event detail modal
- Ticket booking
- Event categories

### ProfileScreen
- User profile management
- Order history
- Settings and preferences
- Support and help

## Deployment

### iOS Deployment

1. **Build for iOS**
   ```bash
   expo build:ios
   ```

2. **Submit to App Store**
   ```bash
   expo upload:ios
   ```

### Android Deployment

1. **Build for Android**
   ```bash
   expo build:android
   ```

2. **Submit to Google Play**
   ```bash
   expo upload:android
   ```

## Configuration

### Environment Variables

Create a `.env` file in the root directory:

```env
EXPO_PUBLIC_API_URL=https://api.lepizzeria.com
EXPO_PUBLIC_GOOGLE_MAPS_API_KEY=your_google_maps_api_key
EXPO_PUBLIC_STRIPE_PUBLISHABLE_KEY=your_stripe_key
```

### Apple Pay Setup

1. Enable Apple Pay in your Apple Developer account
2. Configure merchant identifier
3. Update `app.json` with your Apple Team ID

### Google Pay Setup

1. Set up Google Pay in Google Cloud Console
2. Configure payment methods
3. Add Google Pay credentials

## Testing

```bash
# Run tests
npm test

# Run tests with coverage
npm run test:coverage

# Run linting
npm run lint
```

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests for new features
5. Submit a pull request

## License

This project is licensed under the MIT License - see the LICENSE file for details.

## Support

For support and questions:
- Email: <EMAIL>
- Phone: +27 21 123 4567
- Website: https://lepizzeria.com

## Acknowledgments

- Design inspired by iOS Human Interface Guidelines
- Icons and illustrations from various sources
- React Native community for excellent libraries and tools
