{"name": "lepizzeria-mobile-app", "version": "1.0.0", "description": "Le Pizzeria - Mobile Food Ordering & Event Ticketing App", "main": "App.tsx", "scripts": {"start": "expo start", "android": "expo start --android", "ios": "expo start --ios", "web": "expo start --web", "build": "expo build", "test": "jest", "lint": "eslint . --ext .js,.jsx,.ts,.tsx"}, "dependencies": {"react": "18.2.0", "react-native": "0.72.6", "expo": "~49.0.15", "expo-status-bar": "~1.6.0", "@react-navigation/native": "^6.1.9", "@react-navigation/bottom-tabs": "^6.5.11", "@react-navigation/stack": "^6.3.20", "react-native-screens": "~3.22.0", "react-native-safe-area-context": "4.6.3", "react-native-gesture-handler": "~2.12.0", "react-native-reanimated": "~3.3.0", "react-native-vector-icons": "^10.0.2", "@expo/vector-icons": "^13.0.0", "react-native-maps": "1.7.1", "expo-location": "~16.1.0", "expo-notifications": "~0.20.1", "expo-apple-authentication": "~6.1.0", "expo-google-app-auth": "~8.3.0", "react-native-paper": "^5.11.1", "react-native-elements": "^3.4.3", "react-native-animatable": "^1.3.3", "react-native-modal": "^13.0.1", "react-native-swipe-gestures": "^1.0.5", "react-native-image-picker": "^7.0.3", "react-native-async-storage": "@react-native-async-storage/async-storage", "react-native-keychain": "^8.1.3", "date-fns": "^2.30.0", "uuid": "^9.0.1"}, "devDependencies": {"@babel/core": "^7.20.0", "@types/react": "~18.2.14", "@types/react-native": "~0.72.2", "@types/uuid": "^9.0.7", "typescript": "^5.1.3", "jest": "^29.2.1", "@testing-library/react-native": "^12.4.2", "eslint": "^8.19.0", "@typescript-eslint/eslint-plugin": "^6.7.4", "@typescript-eslint/parser": "^6.7.4"}, "keywords": ["react-native", "expo", "food-delivery", "restaurant", "pizzeria", "mobile-app", "ios", "android"], "author": "Le Pizzeria Team", "license": "MIT", "private": true}