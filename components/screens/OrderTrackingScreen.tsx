import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  ScrollView,
  TouchableOpacity,
  StyleSheet,
  Dimensions,
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { NavigationProps, OrderStatus } from '../../types';

const { width } = Dimensions.get('window');

interface OrderTrackingScreenProps extends NavigationProps {}

const orderStatuses: { status: OrderStatus; label: string; icon: string; time?: string }[] = [
  { status: 'confirmed', label: 'Order Confirmed', icon: '✅', time: '2:15 PM' },
  { status: 'preparing', label: 'Preparing Your Food', icon: '👨‍🍳', time: '2:20 PM' },
  { status: 'ready', label: 'Ready for Pickup', icon: '📦' },
  { status: 'out_for_delivery', label: 'Out for Delivery', icon: '🚗' },
  { status: 'delivered', label: 'Delivered', icon: '🎉' },
];

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f9fafb',
  },
  header: {
    backgroundColor: '#ffffff',
    paddingHorizontal: 16,
    paddingVertical: 12,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    borderBottomWidth: 1,
    borderBottomColor: '#e5e7eb',
  },
  backButton: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: '#f3f4f6',
    alignItems: 'center',
    justifyContent: 'center',
  },
  backIcon: {
    fontSize: 18,
    fontWeight: '600',
  },
  headerTitle: {
    fontSize: 18,
    fontWeight: '700',
    color: '#111827',
    fontFamily: 'Lufga, system-ui, sans-serif',
  },
  helpButton: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: '#f3f4f6',
    alignItems: 'center',
    justifyContent: 'center',
  },
  helpIcon: {
    fontSize: 16,
  },
  orderInfoSection: {
    backgroundColor: '#ffffff',
    padding: 20,
    marginTop: 8,
  },
  orderHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 12,
  },
  orderNumber: {
    fontSize: 18,
    fontWeight: '700',
    color: '#111827',
    fontFamily: 'Lufga, system-ui, sans-serif',
  },
  statusBadge: {
    backgroundColor: '#fef3c7',
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 16,
  },
  statusText: {
    fontSize: 12,
    fontWeight: '600',
    color: '#92400e',
    fontFamily: 'Lufga, system-ui, sans-serif',
  },
  estimatedTimeContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#f0fdf4',
    padding: 12,
    borderRadius: 12,
  },
  estimatedTimeIcon: {
    fontSize: 20,
    marginRight: 8,
  },
  estimatedTimeText: {
    fontSize: 16,
    fontWeight: '600',
    color: '#166534',
    fontFamily: 'Lufga, system-ui, sans-serif',
  },
  mapSection: {
    backgroundColor: '#ffffff',
    marginTop: 8,
    padding: 20,
  },
  mapPlaceholder: {
    height: 200,
    backgroundColor: '#f3f4f6',
    borderRadius: 16,
    alignItems: 'center',
    justifyContent: 'center',
    marginBottom: 16,
  },
  mapIcon: {
    fontSize: 48,
    marginBottom: 8,
  },
  mapText: {
    fontSize: 18,
    fontWeight: '600',
    color: '#111827',
    fontFamily: 'Lufga, system-ui, sans-serif',
    marginBottom: 4,
  },
  mapSubtext: {
    fontSize: 14,
    color: '#6b7280',
    fontFamily: 'Lufga, system-ui, sans-serif',
  },
  driverInfo: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#f9fafb',
    padding: 16,
    borderRadius: 12,
  },
  driverAvatar: {
    width: 48,
    height: 48,
    backgroundColor: '#dc2626',
    borderRadius: 24,
    alignItems: 'center',
    justifyContent: 'center',
    marginRight: 12,
  },
  driverAvatarText: {
    fontSize: 20,
  },
  driverDetails: {
    flex: 1,
  },
  driverName: {
    fontSize: 16,
    fontWeight: '600',
    color: '#111827',
    fontFamily: 'Lufga, system-ui, sans-serif',
    marginBottom: 2,
  },
  driverRating: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  starIcon: {
    fontSize: 14,
    marginRight: 4,
  },
  ratingText: {
    fontSize: 14,
    color: '#6b7280',
    fontFamily: 'Lufga, system-ui, sans-serif',
  },
  callButton: {
    width: 40,
    height: 40,
    backgroundColor: '#22c55e',
    borderRadius: 20,
    alignItems: 'center',
    justifyContent: 'center',
  },
  callIcon: {
    fontSize: 18,
  },
});

export default function OrderTrackingScreen({ navigation }: OrderTrackingScreenProps) {
  const [currentStatus, setCurrentStatus] = useState<OrderStatus>('preparing');
  const [estimatedTime, setEstimatedTime] = useState('25 min');
  const [orderNumber] = useState('#LP2024-001');

  // Simulate order progress
  useEffect(() => {
    const timer = setTimeout(() => {
      if (currentStatus === 'preparing') {
        setCurrentStatus('ready');
        setEstimatedTime('20 min');
      }
    }, 5000);

    return () => clearTimeout(timer);
  }, [currentStatus]);

  const getCurrentStatusIndex = () => {
    return orderStatuses.findIndex(status => status.status === currentStatus);
  };

  const isStatusCompleted = (index: number) => {
    return index <= getCurrentStatusIndex();
  };

  const isStatusCurrent = (index: number) => {
    return index === getCurrentStatusIndex();
  };

  return (
    <SafeAreaView style={styles.container}>
      {/* Header */}
      <View style={styles.header}>
        <TouchableOpacity 
          onPress={() => navigation.goBack()}
          style={styles.backButton}
        >
          <Text style={styles.backIcon}>←</Text>
        </TouchableOpacity>
        <Text style={styles.headerTitle}>Track Order</Text>
        <TouchableOpacity style={styles.helpButton}>
          <Text style={styles.helpIcon}>❓</Text>
        </TouchableOpacity>
      </View>

      <ScrollView showsVerticalScrollIndicator={false}>
        {/* Order Info */}
        <View style={styles.orderInfoSection}>
          <View style={styles.orderHeader}>
            <Text style={styles.orderNumber}>Order {orderNumber}</Text>
            <View style={styles.statusBadge}>
              <Text style={styles.statusText}>
                {orderStatuses[getCurrentStatusIndex()]?.label}
              </Text>
            </View>
          </View>
          
          <View style={styles.estimatedTimeContainer}>
            <Text style={styles.estimatedTimeIcon}>⏰</Text>
            <Text style={styles.estimatedTimeText}>
              Estimated delivery: {estimatedTime}
            </Text>
          </View>
        </View>

        {/* Map Placeholder */}
        <View style={styles.mapSection}>
          <View style={styles.mapPlaceholder}>
            <Text style={styles.mapIcon}>🗺️</Text>
            <Text style={styles.mapText}>Live Tracking Map</Text>
            <Text style={styles.mapSubtext}>Your order is being prepared</Text>
          </View>
          
          {/* Driver Info (when out for delivery) */}
          {currentStatus === 'out_for_delivery' && (
            <View style={styles.driverInfo}>
              <View style={styles.driverAvatar}>
                <Text style={styles.driverAvatarText}>👨‍🚗</Text>
              </View>
              <View style={styles.driverDetails}>
                <Text style={styles.driverName}>Marco Rossi</Text>
                <View style={styles.driverRating}>
                  <Text style={styles.starIcon}>⭐</Text>
                  <Text style={styles.ratingText}>4.9</Text>
                </View>
              </View>
              <TouchableOpacity style={styles.callButton}>
                <Text style={styles.callIcon}>📞</Text>
              </TouchableOpacity>
            </View>
          )}
        </View>

        {/* Order Progress */}
        <View style={styles.progressSection}>
          <Text style={styles.sectionTitle}>Order Progress</Text>
          
          <View style={styles.progressContainer}>
            {orderStatuses.map((status, index) => (
              <View key={status.status} style={styles.progressItem}>
                <View style={styles.progressLeft}>
                  <View style={[
                    styles.progressDot,
                    isStatusCompleted(index) && styles.completedDot,
                    isStatusCurrent(index) && styles.currentDot
                  ]}>
                    {isStatusCompleted(index) && (
                      <Text style={styles.progressIcon}>{status.icon}</Text>
                    )}
                  </View>
                  {index < orderStatuses.length - 1 && (
                    <View style={[
                      styles.progressLine,
                      isStatusCompleted(index) && styles.completedLine
                    ]} />
                  )}
                </View>
                
                <View style={styles.progressContent}>
                  <Text style={[
                    styles.progressLabel,
                    isStatusCompleted(index) && styles.completedLabel,
                    isStatusCurrent(index) && styles.currentLabel
                  ]}>
                    {status.label}
                  </Text>
                  {status.time && (
                    <Text style={styles.progressTime}>{status.time}</Text>
                  )}
                  {isStatusCurrent(index) && (
                    <Text style={styles.currentStatusText}>In progress...</Text>
                  )}
                </View>
              </View>
            ))}
          </View>
        </View>

        {/* Order Details */}
        <View style={styles.orderDetailsSection}>
          <Text style={styles.sectionTitle}>Order Details</Text>
          
          <View style={styles.orderItem}>
            <Text style={styles.itemEmoji}>🍕</Text>
            <View style={styles.itemInfo}>
              <Text style={styles.itemName}>Margherita Classica</Text>
              <Text style={styles.itemDetails}>Medium, Thin Crust</Text>
            </View>
            <Text style={styles.itemQuantity}>x2</Text>
          </View>
          
          <View style={styles.orderItem}>
            <Text style={styles.itemEmoji}>🍝</Text>
            <View style={styles.itemInfo}>
              <Text style={styles.itemName}>Pasta Carbonara</Text>
              <Text style={styles.itemDetails}>Regular portion</Text>
            </View>
            <Text style={styles.itemQuantity}>x1</Text>
          </View>
          
          <View style={styles.orderSummary}>
            <View style={styles.summaryRow}>
              <Text style={styles.summaryLabel}>Subtotal</Text>
              <Text style={styles.summaryValue}>R313.00</Text>
            </View>
            <View style={styles.summaryRow}>
              <Text style={styles.summaryLabel}>Delivery</Text>
              <Text style={styles.summaryValue}>FREE</Text>
            </View>
            <View style={[styles.summaryRow, styles.totalRow]}>
              <Text style={styles.totalLabel}>Total</Text>
              <Text style={styles.totalValue}>R313.00</Text>
            </View>
          </View>
        </View>

        {/* Delivery Address */}
        <View style={styles.addressSection}>
          <Text style={styles.sectionTitle}>Delivery Address</Text>
          <View style={styles.addressContainer}>
            <Text style={styles.addressIcon}>📍</Text>
            <View style={styles.addressDetails}>
              <Text style={styles.addressText}>123 Observatory Road</Text>
              <Text style={styles.addressSubtext}>Observatory, Cape Town 7925</Text>
            </View>
          </View>
        </View>

        {/* Action Buttons */}
        <View style={styles.actionsSection}>
          <TouchableOpacity style={styles.actionButton}>
            <Text style={styles.actionButtonText}>Reorder</Text>
          </TouchableOpacity>
          
          <TouchableOpacity style={[styles.actionButton, styles.primaryButton]}>
            <Text style={[styles.actionButtonText, styles.primaryButtonText]}>
              Rate Order
            </Text>
          </TouchableOpacity>
        </View>

        <View style={styles.bottomPadding} />
      </ScrollView>
    </SafeAreaView>
  );
}

const additionalStyles = StyleSheet.create({
  progressSection: {
    backgroundColor: '#ffffff',
    marginTop: 8,
    padding: 20,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#111827',
    fontFamily: 'Lufga, system-ui, sans-serif',
    marginBottom: 16,
  },
  progressContainer: {
    paddingLeft: 8,
  },
  progressItem: {
    flexDirection: 'row',
    marginBottom: 8,
  },
  progressLeft: {
    alignItems: 'center',
    marginRight: 16,
  },
  progressDot: {
    width: 32,
    height: 32,
    borderRadius: 16,
    backgroundColor: '#e5e7eb',
    alignItems: 'center',
    justifyContent: 'center',
    borderWidth: 2,
    borderColor: '#e5e7eb',
  },
  completedDot: {
    backgroundColor: '#22c55e',
    borderColor: '#22c55e',
  },
  currentDot: {
    backgroundColor: '#fbbf24',
    borderColor: '#fbbf24',
  },
  progressIcon: {
    fontSize: 16,
  },
  progressLine: {
    width: 2,
    height: 40,
    backgroundColor: '#e5e7eb',
    marginTop: 4,
  },
  completedLine: {
    backgroundColor: '#22c55e',
  },
  progressContent: {
    flex: 1,
    paddingTop: 4,
  },
  progressLabel: {
    fontSize: 16,
    fontWeight: '500',
    color: '#6b7280',
    fontFamily: 'Lufga, system-ui, sans-serif',
    marginBottom: 2,
  },
  completedLabel: {
    color: '#111827',
    fontWeight: '600',
  },
  currentLabel: {
    color: '#dc2626',
    fontWeight: '600',
  },
  progressTime: {
    fontSize: 12,
    color: '#9ca3af',
    fontFamily: 'Lufga, system-ui, sans-serif',
  },
  currentStatusText: {
    fontSize: 12,
    color: '#dc2626',
    fontFamily: 'Lufga, system-ui, sans-serif',
    fontStyle: 'italic',
  },
  orderDetailsSection: {
    backgroundColor: '#ffffff',
    marginTop: 8,
    padding: 20,
  },
  orderItem: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 12,
    borderBottomWidth: 1,
    borderBottomColor: '#f3f4f6',
  },
  itemEmoji: {
    fontSize: 24,
    marginRight: 12,
  },
  itemInfo: {
    flex: 1,
  },
  itemName: {
    fontSize: 16,
    fontWeight: '600',
    color: '#111827',
    fontFamily: 'Lufga, system-ui, sans-serif',
    marginBottom: 2,
  },
  itemDetails: {
    fontSize: 14,
    color: '#6b7280',
    fontFamily: 'Lufga, system-ui, sans-serif',
  },
  itemQuantity: {
    fontSize: 16,
    fontWeight: '600',
    color: '#6b7280',
    fontFamily: 'Lufga, system-ui, sans-serif',
  },
  orderSummary: {
    marginTop: 16,
    paddingTop: 16,
    borderTopWidth: 1,
    borderTopColor: '#e5e7eb',
  },
  summaryRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingVertical: 4,
  },
  summaryLabel: {
    fontSize: 14,
    color: '#6b7280',
    fontFamily: 'Lufga, system-ui, sans-serif',
  },
  summaryValue: {
    fontSize: 14,
    fontWeight: '500',
    color: '#111827',
    fontFamily: 'Lufga, system-ui, sans-serif',
  },
  totalRow: {
    borderTopWidth: 1,
    borderTopColor: '#e5e7eb',
    marginTop: 8,
    paddingTop: 8,
  },
  totalLabel: {
    fontSize: 16,
    fontWeight: '700',
    color: '#111827',
    fontFamily: 'Lufga, system-ui, sans-serif',
  },
  totalValue: {
    fontSize: 16,
    fontWeight: '700',
    color: '#dc2626',
    fontFamily: 'Lufga, system-ui, sans-serif',
  },
  addressSection: {
    backgroundColor: '#ffffff',
    marginTop: 8,
    padding: 20,
  },
  addressContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#f9fafb',
    padding: 16,
    borderRadius: 12,
  },
  addressIcon: {
    fontSize: 20,
    marginRight: 12,
  },
  addressDetails: {
    flex: 1,
  },
  addressText: {
    fontSize: 16,
    fontWeight: '600',
    color: '#111827',
    fontFamily: 'Lufga, system-ui, sans-serif',
    marginBottom: 2,
  },
  addressSubtext: {
    fontSize: 14,
    color: '#6b7280',
    fontFamily: 'Lufga, system-ui, sans-serif',
  },
  actionsSection: {
    backgroundColor: '#ffffff',
    marginTop: 8,
    padding: 20,
    flexDirection: 'row',
    gap: 12,
  },
  actionButton: {
    flex: 1,
    backgroundColor: '#f3f4f6',
    paddingVertical: 12,
    borderRadius: 12,
    alignItems: 'center',
  },
  primaryButton: {
    backgroundColor: '#dc2626',
  },
  actionButtonText: {
    fontSize: 16,
    fontWeight: '600',
    color: '#374151',
    fontFamily: 'Lufga, system-ui, sans-serif',
  },
  primaryButtonText: {
    color: '#ffffff',
  },
  bottomPadding: {
    height: 80,
  },
});

// Merge styles
Object.assign(styles, additionalStyles);
