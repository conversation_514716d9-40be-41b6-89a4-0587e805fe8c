import React, { useState } from 'react';
import {
  View,
  Text,
  ScrollView,
  TouchableOpacity,
  StyleSheet,
  FlatList,
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { NavigationProps, CartItem } from '../../types';

interface CartScreenProps extends NavigationProps {}

// Mock cart data
const mockCartItems: CartItem[] = [
  {
    id: '1',
    menuItem: {
      id: '1',
      name: 'Margherita Classica',
      description: 'Fresh mozzarella, basil, tomato sauce',
      price: 89,
      image: '🍕',
      category: 'pizza',
      rating: 4.8,
      prepTime: '15-20 min',
      ingredients: ['Mozzarella', 'Fresh Basil', 'Tomato Sauce'],
      allergens: ['Gluten', 'Dairy'],
      isVegetarian: true,
      isVegan: false,
      isGlutenFree: false,
    },
    quantity: 2,
    customizations: {
      size: ['medium'],
      crust: ['thin']
    },
    totalPrice: 218,
  },
  {
    id: '2',
    menuItem: {
      id: '3',
      name: 'Pasta Carbonara',
      description: 'Creamy sauce with pancetta, parmesan',
      price: 95,
      image: '🍝',
      category: 'pasta',
      rating: 4.7,
      prepTime: '12-18 min',
      ingredients: ['Spaghetti', 'Pancetta', 'Parmesan'],
      allergens: ['Gluten', 'Dairy'],
      isVegetarian: false,
      isVegan: false,
      isGlutenFree: false,
    },
    quantity: 1,
    customizations: {},
    totalPrice: 95,
  }
];

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f9fafb',
  },
  header: {
    backgroundColor: '#ffffff',
    paddingHorizontal: 20,
    paddingVertical: 16,
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    borderBottomWidth: 1,
    borderBottomColor: '#e5e7eb',
  },
  headerTitle: {
    fontSize: 20,
    fontWeight: '700',
    color: '#111827',
    fontFamily: 'Lufga, system-ui, sans-serif',
  },
  clearButton: {
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 8,
    backgroundColor: '#fee2e2',
  },
  clearButtonText: {
    fontSize: 14,
    fontWeight: '500',
    color: '#dc2626',
    fontFamily: 'Lufga, system-ui, sans-serif',
  },
  emptyCart: {
    flex: 1,
    alignItems: 'center',
    justifyContent: 'center',
    paddingHorizontal: 40,
  },
  emptyCartIcon: {
    fontSize: 64,
    marginBottom: 16,
  },
  emptyCartTitle: {
    fontSize: 24,
    fontWeight: '700',
    color: '#111827',
    fontFamily: 'Lufga, system-ui, sans-serif',
    marginBottom: 8,
  },
  emptyCartSubtitle: {
    fontSize: 16,
    color: '#6b7280',
    fontFamily: 'Lufga, system-ui, sans-serif',
    textAlign: 'center',
    marginBottom: 24,
  },
  browseMenuButton: {
    backgroundColor: '#dc2626',
    paddingHorizontal: 24,
    paddingVertical: 12,
    borderRadius: 12,
  },
  browseMenuText: {
    fontSize: 16,
    fontWeight: '600',
    color: '#ffffff',
    fontFamily: 'Lufga, system-ui, sans-serif',
  },
  cartSection: {
    backgroundColor: '#ffffff',
    marginTop: 8,
    paddingVertical: 8,
  },
  cartItem: {
    flexDirection: 'row',
    padding: 16,
    borderBottomWidth: 1,
    borderBottomColor: '#f3f4f6',
  },
  itemImageContainer: {
    width: 60,
    height: 60,
    backgroundColor: '#f3f4f6',
    borderRadius: 12,
    alignItems: 'center',
    justifyContent: 'center',
    marginRight: 12,
  },
  itemEmoji: {
    fontSize: 24,
  },
  itemDetails: {
    flex: 1,
  },
  itemName: {
    fontSize: 16,
    fontWeight: '600',
    color: '#111827',
    fontFamily: 'Lufga, system-ui, sans-serif',
    marginBottom: 4,
  },
  customizations: {
    marginBottom: 4,
  },
  customizationText: {
    fontSize: 12,
    color: '#6b7280',
    fontFamily: 'Lufga, system-ui, sans-serif',
    textTransform: 'capitalize',
  },
  itemPrice: {
    fontSize: 14,
    color: '#6b7280',
    fontFamily: 'Lufga, system-ui, sans-serif',
    marginBottom: 8,
  },
  quantityControls: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#f3f4f6',
    borderRadius: 8,
    padding: 2,
    alignSelf: 'flex-start',
  },
  quantityButton: {
    width: 28,
    height: 28,
    borderRadius: 6,
    backgroundColor: '#ffffff',
    alignItems: 'center',
    justifyContent: 'center',
  },
  quantityButtonText: {
    fontSize: 14,
    fontWeight: '600',
    color: '#374151',
  },
  quantityText: {
    fontSize: 14,
    fontWeight: '600',
    color: '#111827',
    fontFamily: 'Lufga, system-ui, sans-serif',
    marginHorizontal: 12,
    minWidth: 20,
    textAlign: 'center',
  },
  itemTotal: {
    alignItems: 'flex-end',
    justifyContent: 'space-between',
  },
  itemTotalPrice: {
    fontSize: 16,
    fontWeight: '700',
    color: '#111827',
    fontFamily: 'Lufga, system-ui, sans-serif',
  },
  removeButton: {
    padding: 8,
  },
  removeIcon: {
    fontSize: 16,
  },
});

export default function CartScreen({ navigation }: CartScreenProps) {
  const [cartItems, setCartItems] = useState<CartItem[]>(mockCartItems);
  const [promoCode, setPromoCode] = useState('');

  const updateQuantity = (itemId: string, newQuantity: number) => {
    if (newQuantity <= 0) {
      setCartItems(items => items.filter(item => item.id !== itemId));
    } else {
      setCartItems(items =>
        items.map(item =>
          item.id === itemId
            ? { ...item, quantity: newQuantity, totalPrice: item.menuItem.price * newQuantity }
            : item
        )
      );
    }
  };

  const subtotal = cartItems.reduce((sum, item) => sum + item.totalPrice, 0);
  const deliveryFee = subtotal > 150 ? 0 : 25;
  const tax = subtotal * 0.15;
  const total = subtotal + deliveryFee + tax;

  const renderCartItem = ({ item }: { item: CartItem }) => (
    <View style={styles.cartItem}>
      <View style={styles.itemImageContainer}>
        <Text style={styles.itemEmoji}>{item.menuItem.image}</Text>
      </View>
      
      <View style={styles.itemDetails}>
        <Text style={styles.itemName}>{item.menuItem.name}</Text>
        
        {/* Customizations */}
        {Object.keys(item.customizations).length > 0 && (
          <View style={styles.customizations}>
            {Object.entries(item.customizations).map(([key, values]) => (
              <Text key={key} style={styles.customizationText}>
                {key}: {values.join(', ')}
              </Text>
            ))}
          </View>
        )}
        
        <Text style={styles.itemPrice}>R{item.menuItem.price} each</Text>
        
        <View style={styles.quantityControls}>
          <TouchableOpacity
            onPress={() => updateQuantity(item.id, item.quantity - 1)}
            style={styles.quantityButton}
          >
            <Text style={styles.quantityButtonText}>−</Text>
          </TouchableOpacity>
          <Text style={styles.quantityText}>{item.quantity}</Text>
          <TouchableOpacity
            onPress={() => updateQuantity(item.id, item.quantity + 1)}
            style={styles.quantityButton}
          >
            <Text style={styles.quantityButtonText}>+</Text>
          </TouchableOpacity>
        </View>
      </View>
      
      <View style={styles.itemTotal}>
        <Text style={styles.itemTotalPrice}>R{item.totalPrice}</Text>
        <TouchableOpacity
          onPress={() => updateQuantity(item.id, 0)}
          style={styles.removeButton}
        >
          <Text style={styles.removeIcon}>🗑️</Text>
        </TouchableOpacity>
      </View>
    </View>
  );

  if (cartItems.length === 0) {
    return (
      <SafeAreaView style={styles.container}>
        <View style={styles.header}>
          <Text style={styles.headerTitle}>Cart</Text>
        </View>
        <View style={styles.emptyCart}>
          <Text style={styles.emptyCartIcon}>🛒</Text>
          <Text style={styles.emptyCartTitle}>Your cart is empty</Text>
          <Text style={styles.emptyCartSubtitle}>
            Add some delicious items from our menu
          </Text>
          <TouchableOpacity
            style={styles.browseMenuButton}
            onPress={() => navigation.navigate('Menu')}
          >
            <Text style={styles.browseMenuText}>Browse Menu</Text>
          </TouchableOpacity>
        </View>
      </SafeAreaView>
    );
  }

  return (
    <SafeAreaView style={styles.container}>
      {/* Header */}
      <View style={styles.header}>
        <Text style={styles.headerTitle}>Cart ({cartItems.length})</Text>
        <TouchableOpacity style={styles.clearButton}>
          <Text style={styles.clearButtonText}>Clear All</Text>
        </TouchableOpacity>
      </View>

      <ScrollView showsVerticalScrollIndicator={false}>
        {/* Cart Items */}
        <View style={styles.cartSection}>
          <FlatList
            data={cartItems}
            renderItem={renderCartItem}
            keyExtractor={(item) => item.id}
            scrollEnabled={false}
          />
        </View>

        {/* Promo Code */}
        <View style={styles.promoSection}>
          <Text style={styles.sectionTitle}>Promo Code</Text>
          <View style={styles.promoInputContainer}>
            <Text style={styles.promoInput}>Enter promo code</Text>
            <TouchableOpacity style={styles.applyButton}>
              <Text style={styles.applyButtonText}>Apply</Text>
            </TouchableOpacity>
          </View>
        </View>

        {/* Order Summary */}
        <View style={styles.summarySection}>
          <Text style={styles.sectionTitle}>Order Summary</Text>
          
          <View style={styles.summaryRow}>
            <Text style={styles.summaryLabel}>Subtotal</Text>
            <Text style={styles.summaryValue}>R{subtotal.toFixed(2)}</Text>
          </View>
          
          <View style={styles.summaryRow}>
            <Text style={styles.summaryLabel}>Delivery Fee</Text>
            <Text style={styles.summaryValue}>
              {deliveryFee === 0 ? 'FREE' : `R${deliveryFee.toFixed(2)}`}
            </Text>
          </View>
          
          <View style={styles.summaryRow}>
            <Text style={styles.summaryLabel}>Tax (15%)</Text>
            <Text style={styles.summaryValue}>R{tax.toFixed(2)}</Text>
          </View>
          
          <View style={[styles.summaryRow, styles.totalRow]}>
            <Text style={styles.totalLabel}>Total</Text>
            <Text style={styles.totalValue}>R{total.toFixed(2)}</Text>
          </View>
        </View>

        {/* Delivery Info */}
        <View style={styles.deliverySection}>
          <Text style={styles.sectionTitle}>Delivery Information</Text>
          <View style={styles.deliveryInfo}>
            <Text style={styles.deliveryIcon}>📍</Text>
            <View style={styles.deliveryDetails}>
              <Text style={styles.deliveryAddress}>Observatory, Cape Town</Text>
              <Text style={styles.deliveryTime}>Estimated delivery: 25-35 min</Text>
            </View>
            <TouchableOpacity style={styles.changeButton}>
              <Text style={styles.changeButtonText}>Change</Text>
            </TouchableOpacity>
          </View>
        </View>

        <View style={styles.bottomPadding} />
      </ScrollView>

      {/* Checkout Button */}
      <View style={styles.checkoutContainer}>
        <TouchableOpacity 
          style={styles.checkoutButton}
          onPress={() => navigation.navigate('OrderTracking')}
        >
          <Text style={styles.checkoutButtonText}>
            Proceed to Checkout • R{total.toFixed(2)}
          </Text>
        </TouchableOpacity>
      </View>
    </SafeAreaView>
  );
}
