import React, { useState } from 'react';
import {
  View,
  Text,
  ScrollView,
  TouchableOpacity,
  StyleSheet,
  FlatList,
  Dimensions,
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { NavigationProps, Event, EventCategory } from '../../types';
import { mockEvents } from '../../utils/mockData';

const { width } = Dimensions.get('window');

interface EventsScreenProps extends NavigationProps {}

const eventCategories = [
  { id: 'all', name: 'All Events', icon: '🎫' },
  { id: 'wine_tasting', name: 'Wine Tasting', icon: '🍷' },
  { id: 'cooking_class', name: 'Cooking Class', icon: '👨‍🍳' },
  { id: 'live_music', name: 'Live Music', icon: '🎵' },
  { id: 'private_dining', name: 'Private Dining', icon: '🍽️' },
];

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f9fafb',
  },
  header: {
    backgroundColor: '#ffffff',
    paddingHorizontal: 20,
    paddingVertical: 16,
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    borderBottomWidth: 1,
    borderBottomColor: '#e5e7eb',
  },
  headerTitle: {
    fontSize: 20,
    fontWeight: '700',
    color: '#111827',
    fontFamily: 'Lufga, system-ui, sans-serif',
  },
  calendarButton: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: '#f3f4f6',
    alignItems: 'center',
    justifyContent: 'center',
  },
  calendarIcon: {
    fontSize: 18,
  },
  heroSection: {
    backgroundColor: '#dc2626',
    paddingHorizontal: 20,
    paddingVertical: 24,
    alignItems: 'center',
  },
  heroTitle: {
    fontSize: 24,
    fontWeight: '700',
    color: '#ffffff',
    fontFamily: 'Lufga, system-ui, sans-serif',
    marginBottom: 8,
    textAlign: 'center',
  },
  heroSubtitle: {
    fontSize: 16,
    color: '#fecaca',
    fontFamily: 'Lufga, system-ui, sans-serif',
    textAlign: 'center',
    lineHeight: 24,
  },
  categoriesSection: {
    backgroundColor: '#ffffff',
    paddingVertical: 16,
    borderBottomWidth: 1,
    borderBottomColor: '#e5e7eb',
  },
  categoriesContainer: {
    flexDirection: 'row',
    paddingHorizontal: 20,
    gap: 12,
  },
  categoryButton: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderRadius: 20,
    backgroundColor: '#f3f4f6',
    gap: 6,
  },
  activeCategoryButton: {
    backgroundColor: '#dc2626',
  },
  categoryIcon: {
    fontSize: 16,
  },
  categoryText: {
    fontSize: 14,
    fontWeight: '500',
    color: '#6b7280',
    fontFamily: 'Lufga, system-ui, sans-serif',
  },
  activeCategoryText: {
    color: '#ffffff',
    fontWeight: '600',
  },
  eventsSection: {
    flex: 1,
    paddingHorizontal: 20,
  },
  sectionHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingVertical: 16,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#111827',
    fontFamily: 'Lufga, system-ui, sans-serif',
  },
  eventCount: {
    fontSize: 14,
    color: '#6b7280',
    fontFamily: 'Lufga, system-ui, sans-serif',
  },
  eventsList: {
    paddingBottom: 100,
  },
  eventCard: {
    backgroundColor: '#ffffff',
    borderRadius: 16,
    padding: 16,
    marginBottom: 16,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  eventImageContainer: {
    height: 120,
    backgroundColor: '#f3f4f6',
    borderRadius: 12,
    alignItems: 'center',
    justifyContent: 'center',
    marginBottom: 12,
    position: 'relative',
  },
  eventEmoji: {
    fontSize: 48,
  },
  eventBadge: {
    position: 'absolute',
    top: 8,
    right: 8,
    backgroundColor: '#dc2626',
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 12,
  },
  eventBadgeText: {
    fontSize: 12,
    fontWeight: '600',
    color: '#ffffff',
  },
  eventInfo: {
    gap: 8,
  },
  eventHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
  },
  eventTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#111827',
    fontFamily: 'Lufga, system-ui, sans-serif',
    flex: 1,
    marginRight: 8,
  },
  eventPrice: {
    fontSize: 18,
    fontWeight: '700',
    color: '#dc2626',
    fontFamily: 'Lufga, system-ui, sans-serif',
  },
  eventDescription: {
    fontSize: 14,
    color: '#6b7280',
    fontFamily: 'Lufga, system-ui, sans-serif',
    lineHeight: 20,
  },
  eventDetails: {
    gap: 4,
  },
  eventDetailItem: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 6,
  },
  eventDetailIcon: {
    fontSize: 14,
  },
  eventDetailText: {
    fontSize: 14,
    color: '#374151',
    fontFamily: 'Lufga, system-ui, sans-serif',
    flex: 1,
  },
  eventFeatures: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: 6,
    alignItems: 'center',
  },
  featureTag: {
    backgroundColor: '#f0fdf4',
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 12,
  },
  featureText: {
    fontSize: 12,
    color: '#166534',
    fontFamily: 'Lufga, system-ui, sans-serif',
  },
  moreFeatures: {
    fontSize: 12,
    color: '#6b7280',
    fontFamily: 'Lufga, system-ui, sans-serif',
  },
});

export default function EventsScreen({ navigation }: EventsScreenProps) {
  const [activeCategory, setActiveCategory] = useState<EventCategory | 'all'>('all');
  const [selectedEvent, setSelectedEvent] = useState<Event | null>(null);

  const filteredEvents = mockEvents.filter(event => 
    activeCategory === 'all' || event.category === activeCategory
  );

  const formatDate = (date: Date) => {
    return date.toLocaleDateString('en-US', {
      weekday: 'short',
      month: 'short',
      day: 'numeric'
    });
  };

  const formatTime = (time: string) => {
    return time;
  };

  const renderEventCard = ({ item }: { item: Event }) => (
    <TouchableOpacity
      style={styles.eventCard}
      onPress={() => setSelectedEvent(item)}
    >
      <View style={styles.eventImageContainer}>
        <Text style={styles.eventEmoji}>{item.image}</Text>
        <View style={styles.eventBadge}>
          <Text style={styles.eventBadgeText}>
            {item.availableTickets} left
          </Text>
        </View>
      </View>
      
      <View style={styles.eventInfo}>
        <View style={styles.eventHeader}>
          <Text style={styles.eventTitle} numberOfLines={2}>
            {item.title}
          </Text>
          <Text style={styles.eventPrice}>R{item.price}</Text>
        </View>
        
        <Text style={styles.eventDescription} numberOfLines={2}>
          {item.description}
        </Text>
        
        <View style={styles.eventDetails}>
          <View style={styles.eventDetailItem}>
            <Text style={styles.eventDetailIcon}>📅</Text>
            <Text style={styles.eventDetailText}>
              {formatDate(item.date)}
            </Text>
          </View>
          
          <View style={styles.eventDetailItem}>
            <Text style={styles.eventDetailIcon}>⏰</Text>
            <Text style={styles.eventDetailText}>
              {formatTime(item.startTime)}
            </Text>
          </View>
          
          <View style={styles.eventDetailItem}>
            <Text style={styles.eventDetailIcon}>📍</Text>
            <Text style={styles.eventDetailText} numberOfLines={1}>
              {item.location}
            </Text>
          </View>
        </View>
        
        <View style={styles.eventFeatures}>
          {item.features.slice(0, 2).map((feature, index) => (
            <View key={index} style={styles.featureTag}>
              <Text style={styles.featureText}>{feature}</Text>
            </View>
          ))}
          {item.features.length > 2 && (
            <Text style={styles.moreFeatures}>
              +{item.features.length - 2} more
            </Text>
          )}
        </View>
      </View>
    </TouchableOpacity>
  );

  const EventDetailModal = () => {
    if (!selectedEvent) return null;

    return (
      <View style={styles.modalOverlay}>
        <View style={styles.modalContent}>
          <ScrollView showsVerticalScrollIndicator={false}>
            {/* Modal Header */}
            <View style={styles.modalHeader}>
              <TouchableOpacity
                onPress={() => setSelectedEvent(null)}
                style={styles.closeButton}
              >
                <Text style={styles.closeIcon}>✕</Text>
              </TouchableOpacity>
            </View>

            {/* Event Image */}
            <View style={styles.modalEventImage}>
              <Text style={styles.modalEventEmoji}>{selectedEvent.image}</Text>
            </View>

            {/* Event Info */}
            <View style={styles.modalEventInfo}>
              <Text style={styles.modalEventTitle}>{selectedEvent.title}</Text>
              <Text style={styles.modalEventDescription}>
                {selectedEvent.description}
              </Text>

              {/* Event Details */}
              <View style={styles.modalEventDetails}>
                <View style={styles.modalDetailRow}>
                  <Text style={styles.modalDetailIcon}>📅</Text>
                  <Text style={styles.modalDetailText}>
                    {formatDate(selectedEvent.date)}
                  </Text>
                </View>
                
                <View style={styles.modalDetailRow}>
                  <Text style={styles.modalDetailIcon}>⏰</Text>
                  <Text style={styles.modalDetailText}>
                    {selectedEvent.startTime} - {selectedEvent.endTime}
                  </Text>
                </View>
                
                <View style={styles.modalDetailRow}>
                  <Text style={styles.modalDetailIcon}>📍</Text>
                  <Text style={styles.modalDetailText}>
                    {selectedEvent.location}
                  </Text>
                </View>
                
                <View style={styles.modalDetailRow}>
                  <Text style={styles.modalDetailIcon}>👥</Text>
                  <Text style={styles.modalDetailText}>
                    {selectedEvent.availableTickets} of {selectedEvent.capacity} spots available
                  </Text>
                </View>
              </View>

              {/* Features */}
              <View style={styles.modalFeatures}>
                <Text style={styles.modalSectionTitle}>What's Included</Text>
                <View style={styles.modalFeaturesList}>
                  {selectedEvent.features.map((feature, index) => (
                    <View key={index} style={styles.modalFeatureItem}>
                      <Text style={styles.modalFeatureIcon}>✓</Text>
                      <Text style={styles.modalFeatureText}>{feature}</Text>
                    </View>
                  ))}
                </View>
              </View>
            </View>
          </ScrollView>

          {/* Book Button */}
          <View style={styles.modalFooter}>
            <View style={styles.priceContainer}>
              <Text style={styles.priceLabel}>Price per person</Text>
              <Text style={styles.priceValue}>R{selectedEvent.price}</Text>
            </View>
            <TouchableOpacity style={styles.bookButton}>
              <Text style={styles.bookButtonText}>Book Now</Text>
            </TouchableOpacity>
          </View>
        </View>
      </View>
    );
  };

  return (
    <SafeAreaView style={styles.container}>
      {/* Header */}
      <View style={styles.header}>
        <Text style={styles.headerTitle}>Events</Text>
        <TouchableOpacity style={styles.calendarButton}>
          <Text style={styles.calendarIcon}>📅</Text>
        </TouchableOpacity>
      </View>

      {/* Hero Section */}
      <View style={styles.heroSection}>
        <Text style={styles.heroTitle}>Experience Le Pizzeria</Text>
        <Text style={styles.heroSubtitle}>
          Join us for exclusive culinary experiences and special events
        </Text>
      </View>

      {/* Categories */}
      <View style={styles.categoriesSection}>
        <ScrollView horizontal showsHorizontalScrollIndicator={false}>
          <View style={styles.categoriesContainer}>
            {eventCategories.map((category) => (
              <TouchableOpacity
                key={category.id}
                onPress={() => setActiveCategory(category.id as EventCategory | 'all')}
                style={[
                  styles.categoryButton,
                  activeCategory === category.id && styles.activeCategoryButton
                ]}
              >
                <Text style={styles.categoryIcon}>{category.icon}</Text>
                <Text style={[
                  styles.categoryText,
                  activeCategory === category.id && styles.activeCategoryText
                ]}>
                  {category.name}
                </Text>
              </TouchableOpacity>
            ))}
          </View>
        </ScrollView>
      </View>

      {/* Events List */}
      <View style={styles.eventsSection}>
        <View style={styles.sectionHeader}>
          <Text style={styles.sectionTitle}>Upcoming Events</Text>
          <Text style={styles.eventCount}>
            {filteredEvents.length} event{filteredEvents.length !== 1 ? 's' : ''}
          </Text>
        </View>

        <FlatList
          data={filteredEvents}
          renderItem={renderEventCard}
          keyExtractor={(item) => item.id}
          showsVerticalScrollIndicator={false}
          contentContainerStyle={styles.eventsList}
        />
      </View>

      {/* Event Detail Modal */}
      {selectedEvent && <EventDetailModal />}
    </SafeAreaView>
  );
}

const modalStyles = StyleSheet.create({
  modalOverlay: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'flex-end',
  },
  modalContent: {
    backgroundColor: '#ffffff',
    borderTopLeftRadius: 20,
    borderTopRightRadius: 20,
    maxHeight: '90%',
    paddingBottom: 20,
  },
  modalHeader: {
    flexDirection: 'row',
    justifyContent: 'flex-end',
    padding: 16,
  },
  closeButton: {
    width: 32,
    height: 32,
    borderRadius: 16,
    backgroundColor: '#f3f4f6',
    alignItems: 'center',
    justifyContent: 'center',
  },
  closeIcon: {
    fontSize: 16,
    fontWeight: '600',
    color: '#6b7280',
  },
  modalEventImage: {
    height: 150,
    backgroundColor: '#f3f4f6',
    marginHorizontal: 20,
    borderRadius: 16,
    alignItems: 'center',
    justifyContent: 'center',
    marginBottom: 20,
  },
  modalEventEmoji: {
    fontSize: 64,
  },
  modalEventInfo: {
    paddingHorizontal: 20,
  },
  modalEventTitle: {
    fontSize: 24,
    fontWeight: '700',
    color: '#111827',
    fontFamily: 'Lufga, system-ui, sans-serif',
    marginBottom: 8,
  },
  modalEventDescription: {
    fontSize: 16,
    color: '#6b7280',
    fontFamily: 'Lufga, system-ui, sans-serif',
    lineHeight: 24,
    marginBottom: 20,
  },
  modalEventDetails: {
    backgroundColor: '#f9fafb',
    borderRadius: 12,
    padding: 16,
    marginBottom: 20,
  },
  modalDetailRow: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 12,
  },
  modalDetailIcon: {
    fontSize: 18,
    marginRight: 12,
    width: 24,
  },
  modalDetailText: {
    fontSize: 16,
    color: '#374151',
    fontFamily: 'Lufga, system-ui, sans-serif',
    flex: 1,
  },
  modalFeatures: {
    marginBottom: 20,
  },
  modalSectionTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#111827',
    fontFamily: 'Lufga, system-ui, sans-serif',
    marginBottom: 12,
  },
  modalFeaturesList: {
    gap: 8,
  },
  modalFeatureItem: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  modalFeatureIcon: {
    fontSize: 16,
    color: '#22c55e',
    marginRight: 12,
    fontWeight: '600',
  },
  modalFeatureText: {
    fontSize: 16,
    color: '#374151',
    fontFamily: 'Lufga, system-ui, sans-serif',
    flex: 1,
  },
  modalFooter: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 20,
    paddingTop: 16,
    borderTopWidth: 1,
    borderTopColor: '#e5e7eb',
    gap: 16,
  },
  priceContainer: {
    flex: 1,
  },
  priceLabel: {
    fontSize: 14,
    color: '#6b7280',
    fontFamily: 'Lufga, system-ui, sans-serif',
    marginBottom: 2,
  },
  priceValue: {
    fontSize: 20,
    fontWeight: '700',
    color: '#dc2626',
    fontFamily: 'Lufga, system-ui, sans-serif',
  },
  bookButton: {
    backgroundColor: '#dc2626',
    paddingHorizontal: 24,
    paddingVertical: 12,
    borderRadius: 12,
  },
  bookButtonText: {
    fontSize: 16,
    fontWeight: '600',
    color: '#ffffff',
    fontFamily: 'Lufga, system-ui, sans-serif',
  },
});

// Merge styles
Object.assign(styles, modalStyles);
